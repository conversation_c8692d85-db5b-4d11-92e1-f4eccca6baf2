# LMB Server - NestJS Backend API

## Project Overview

LMB is a fishing community platform built with a modern tech stack:

- **Backend**: NestJS (TypeScript)
- **Database**: PostgreSQL with TypeORM
- **Web Frontend**: Next.js (React)
- **Mobile Apps**: Expo/React Native (iOS and Android)
- **Authentication**: Social login only (Google, Facebook, GitHub, Apple)

## Core Features

- User profiles and social connections
- Fishing communities with moderation
- Fishing spots with location data
- Posts, comments, and content sharing
- Guide and vendor profiles
- Content moderation and reporting
- Analytics and activity tracking
- Real-time notifications

## Authentication Approach

- **Social-only authentication** (no email/password)
- **HttpOnly cookie authentication** for secure web sessions
- **JWT tokens** with dual support (Authorization headers + cookies)
- **Custom NestJS OAuth endpoints** with GitHub integration
- **Enterprise-grade CSRF protection** for all state-changing requests
- **Comprehensive security headers** with Helmet middleware
- **OAuth token revocation** for complete logout security

## Data Model

The application uses a comprehensive type system defined in shared `types/global.ts`, including:

- User management (profiles, preferences, activity)
- Content types (posts, comments, fishing spots)
- Community features (membership, roles, moderation)
- Authentication (sessions, tokens, providers)
- Security (audit logs, rate limiting, device tracking)

## Current File Structure

```typescript
src/
├── main.ts                          # Application entry point
├── app.module.ts                    # Root module with guards
├── app.controller.ts                # Root controller (health, init-db)
├── app.service.ts                   # Root service
├── types/                           # Type definitions (symlinked from ../types)
│   └── global.ts                    # Shared TypeScript types
├── config/                          # Configuration
│   ├── config.module.ts             # Configuration module
│   ├── config.service.ts            # Environment and app config
│   └── validation.schema.ts         # Config validation schema
├── auth/                            # ✅ Authentication (COMPLETE)
│   ├── auth.module.ts               # Auth module
│   ├── auth.controller.ts           # OAuth & profile endpoints
│   ├── auth.service.ts              # Auth business logic
│   ├── guards/                      # Auth guards
│   │   ├── jwt.guard.ts             # JWT authentication guard
│   │   └── github.guard.ts          # GitHub OAuth guard
│   ├── strategies/                  # Auth strategies
│   │   ├── github.strategy.ts       # GitHub OAuth strategy
│   │   └── jwt.strategy.ts          # JWT strategy
│   └── dto/                         # Data transfer objects
│       └── auth.dto.ts              # Auth-related DTOs
├── security/                        # ✅ Security (COMPLETE)
│   ├── security.module.ts           # Security module
│   ├── security.controller.ts       # CSRF token endpoint
│   ├── security.service.ts          # CSRF token generation
│   ├── guards/                      # Security guards
│   │   └── csrf.guard.ts            # CSRF protection guard
│   └── decorators/                  # Security decorators
│       └── skip-csrf.decorator.ts   # Skip CSRF decorator
├── database/                        # ✅ Database (COMPLETE)
│   ├── database.module.ts           # TypeORM module
│   ├── database.service.ts          # Database health service
│   ├── data-source.ts               # TypeORM data source
│   ├── entities/                    # TypeORM entity definitions
│   │   ├── user.entity.ts           # User profiles and auth
│   │   ├── auth-token.entity.ts     # OAuth tokens
│   │   ├── session.entity.ts        # User sessions
│   │   ├── community.entity.ts      # Fishing communities
│   │   ├── community-member.entity.ts # Community membership
│   │   ├── community-invite.entity.ts # Community invitations
│   │   ├── post.entity.ts           # User posts
│   │   ├── comment.entity.ts        # Threaded comments
│   │   ├── tag.entity.ts            # Content tags (with pending approval system)
│   │   ├── fishing-spot.entity.ts   # Location data (with tag integration)
│   │   ├── user-follow.entity.ts    # Social relationships
│   │   ├── post-vote.entity.ts      # Post voting
│   │   ├── comment-vote.entity.ts   # Comment voting
│   │   └── index.ts                 # Entity exports
│   ├── migrations/                  # Database migrations
│   │   └── 1737000000000-InitialSchema.ts
│   └── repositories/                # Data access layer
│       ├── user.repository.ts       # User data access
│       ├── community.repository.ts  # Community data access
│       ├── post.repository.ts       # Post data access
│       ├── comment.repository.ts    # Comment data access
│       ├── tag.repository.ts        # Tag data access
│       ├── fishing-spot.repository.ts # Fishing spot data access
│       └── index.ts                 # Repository exports
├── services/                        # Shared services
│   └── username-generator.service.ts # Username generation
├── common/                          # Shared code
│   └── decorators/                  # Custom decorators
│       └── public.decorator.ts      # Public endpoint decorator
├── users/                           # ✅ User Management (COMPLETE)
│   ├── users.module.ts              # Users module
│   ├── users.controller.ts          # User endpoints
│   ├── users.service.ts             # User business logic
│   ├── users.controller.spec.ts     # Controller tests
│   ├── users.service.spec.ts        # Service tests
│   └── dto/                         # User DTOs
│       └── users.dto.ts             # User data transfer objects
├── communities/                     # ✅ Communities (COMPLETE)
│   ├── communities.module.ts        # Communities module
│   ├── communities.controller.ts    # Community endpoints
│   ├── communities.service.ts       # Community business logic
│   ├── communities.controller.spec.ts # Controller tests
│   ├── communities.service.spec.ts  # Service tests
│   └── dto/                         # Community DTOs
│       └── communities.dto.ts       # Community data transfer objects
├── posts/                           # ✅ Posts (COMPLETE)
│   ├── posts.module.ts              # Posts module
│   ├── posts.controller.ts          # Post endpoints
│   ├── posts.service.ts             # Post business logic
│   ├── posts.controller.spec.ts     # Controller tests
│   ├── posts.service.spec.ts        # Service tests
│   └── dto/                         # Post DTOs
│       └── posts.dto.ts             # Post data transfer objects
├── comments/                        # ✅ Comments (COMPLETE)
│   ├── comments.module.ts           # Comments module
│   ├── comments.controller.ts       # Comment endpoints (3 controllers)
│   ├── comments.service.ts          # Comment business logic
│   ├── comments.controller.spec.ts  # Controller tests
│   ├── comments.service.spec.ts     # Service tests
│   └── dto/                         # Comment DTOs
│       └── comments.dto.ts          # Comment data transfer objects
├── tags/                            # ✅ Tags (COMPLETE)
│   ├── tags.module.ts               # Tags module
│   ├── tags.controller.ts           # Tag endpoints
│   ├── tags.service.ts              # Tag business logic
│   ├── tags.controller.spec.ts      # Controller tests
│   ├── tags.service.spec.ts         # Service tests
│   └── dto/                         # Tag DTOs
│       └── tags.dto.ts              # Tag data transfer objects
├── fishing-spots/                   # ✅ Fishing Spots (COMPLETE)
│   ├── fishing-spots.module.ts      # Fishing spots module
│   ├── fishing-spots.controller.ts  # Fishing spot endpoints (3 controllers)
│   ├── fishing-spots.service.ts     # Fishing spot business logic
│   ├── fishing-spots.controller.spec.ts # Controller tests
│   ├── fishing-spots.service.spec.ts # Service tests
│   └── dto/                         # Fishing spot DTOs
│       └── fishing-spots.dto.ts     # Fishing spot data transfer objects
├── services/                        # Shared services
│   ├── username-generator.service.ts # Username generation
│   └── tag-normalization.service.ts # Tag name normalization and validation
└── [PLANNED MODULES]                # 🚧 To be implemented
    ├── notifications/               # Notification system
    ├── moderation/                  # Content moderation
    └── analytics/                   # Analytics tracking
```

## Development Approach

1. **TypeScript Throughout**: Consistent typing across all platforms
2. **Feature-Based Structure**: Organize code by domain features
3. **API-First Development**: Define and implement API contracts before UI
4. **Shared Code**: Maximize code reuse between platforms
5. **Progressive Enhancement**: Build core features first, then enhance

## Security Considerations

- **CSRF Protection**: Enterprise-grade CSRF token validation for all state-changing requests
- **Security Headers**: Comprehensive HTTP security headers via Helmet middleware
- **Token Security**: Tokens stored separately from user data in httpOnly cookies
- **Session Security**: Sessions don't contain sensitive user information
- **Audit Logging**: Comprehensive security event logging and monitoring
- **Device Management**: Session control and device tracking for security
- **Rate Limiting**: Multi-layer rate limiting to prevent abuse and attacks
- **Input Validation**: Comprehensive request validation and sanitization
- **OWASP Compliance**: Protection against OWASP Top 10 security vulnerabilities

## Development Setup

### Prerequisites

- Node.js and pnpm
- PostgreSQL instance running locally or remotely
- Expo CLI for mobile development

### Environment Variables

Create a `.env` file in the server directory with:

```env
# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=lmb
DATABASE_USER=lmbuser
DATABASE_PASSWORD=your_password_here

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# GitHub OAuth Configuration
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_CALLBACK_URL=http://localhost:3000/v1/auth/github/callback

# Application Configuration
NODE_ENV=development
PORT=3000
APP_URL=http://localhost:3000

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=10

# Web Frontend URL (for OAuth redirects)
WEB_URL=http://localhost:3001
```

### Database Setup

After setting up PostgreSQL and environment variables:

```bash
cd server

# Install dependencies
pnpm install

# Initialize database schema
curl -X POST http://localhost:3000/v1/init-db

# Or run migrations (alternative approach)
pnpm migration:run
```

### Running the Application

```bash
# Start the backend server
cd server
pnpm start:dev

# Start the web application (separate terminal)
cd web
pnpm dev

# Start the mobile application (when implemented)
cd mobile
pnpm start
```

## API Endpoints

### ✅ Currently Implemented

**Authentication & Security:**

- `GET /v1/auth/github` - Initiate GitHub OAuth flow
- `GET /v1/auth/github/callback` - GitHub OAuth callback handler
- `GET /v1/auth/profile` - Get authenticated user profile (JWT protected)
- `POST /v1/auth/logout` - User logout with OAuth token revocation
- `GET /v1/auth/health` - Authentication service health check
- `GET /v1/security/csrf-token` - Generate CSRF token for state-changing requests

**User Management:**

- `GET /v1/users/me` - Get current user's full profile (JWT protected)
- `PUT /v1/users/me` - Update current user profile (JWT + CSRF protected)
- `GET /v1/users/:id` - Get public user profile (accepts both user ID and username)
- `GET /v1/users/search` - Search users by username/display name with pagination
- `GET /v1/users/me/followers` - Get current user's followers (JWT protected)
- `GET /v1/users/me/following` - Get who current user follows (JWT protected)
- `GET /v1/users/:userId/followers` - Get user's followers
- `GET /v1/users/:userId/following` - Get who user follows
- `POST /v1/users/:userId/follow` - Follow another user (JWT + CSRF protected)
- `DELETE /v1/users/:userId/follow` - Unfollow user (JWT + CSRF protected)

**Community Management:**

- `GET /v1/communities` - List public communities with search and pagination
- `POST /v1/communities` - Create new community (JWT + CSRF protected)
- `GET /v1/communities/:id` - Get community details (public/authenticated)
- `PUT /v1/communities/:id` - Update community (owner/admin only, JWT + CSRF protected)
- `DELETE /v1/communities/:id` - Delete community (owner only, JWT + CSRF protected)
- `POST /v1/communities/:id/join` - Join a public community (JWT + CSRF protected)
- `DELETE /v1/communities/:id/leave` - Leave a community (JWT + CSRF protected)
- `GET /v1/communities/:id/members` - List community members with pagination
- `PUT /v1/communities/:id/members/:userId/role` - Update member role (admin/owner only, JWT + CSRF protected)
- `GET /v1/communities/me/communities` - Get user's communities (JWT protected)
- `GET /v1/communities/me/invites` - Get user's invitations (JWT protected)
- `POST /v1/communities/:id/invite` - Create community invitation (JWT + CSRF protected)
- `GET /v1/communities/:id/invites` - List community invitations (moderator+, JWT protected)
- `POST /v1/communities/invites/:inviteId/accept` - Accept invitation (JWT + CSRF protected)
- `POST /v1/communities/invites/:inviteId/reject` - Reject invitation (JWT + CSRF protected)
- `DELETE /v1/communities/invites/:inviteId` - Delete invitation (JWT + CSRF protected)

**Community Features:**

- **Role Hierarchy:** Member → Moderator → Admin → Owner
- **Visibility Settings:** Public (anyone can join) vs Private (invitation only)
- **Invite Permissions:** Configurable who can invite (Anyone, Members, Moderators, Admins)
- **Content Moderation:** Settings for post and comment moderation
- **Search & Discovery:** Full-text search across community names and descriptions
- **Comprehensive Testing:** 28 passing tests (14 service + 14 controller tests)

**Posts (`/v1/posts`):**

- `GET /v1/posts` - List posts with search and filtering (supports community, author, status, tags, search query)
- `POST /v1/posts` - Create new post (requires authentication and community membership)
- `GET /v1/posts/:id` - Get post details with view tracking
- `PUT /v1/posts/:id` - Update post (author or moderator only)
- `DELETE /v1/posts/:id` - Delete post (author or moderator only)
- `POST /v1/posts/:id/vote` - Vote on post (upvote/downvote, prevents self-voting)
- `DELETE /v1/posts/:id/vote` - Remove vote from post
- `GET /v1/posts/:id/votes` - Get post vote statistics with user's vote status
- `GET /v1/posts/community/:communityId` - Get posts by community (respects privacy settings)
- `GET /v1/posts/author/:authorId` - Get posts by author (filters private community posts)
- `POST /v1/posts/:id/moderate` - Moderate post status (moderators and admins only)

**Features:**

- **Permission System:** Community membership validation for posting and voting
- **Privacy Controls:** Automatic filtering of private community content
- **Vote Management:** Comprehensive voting system with duplicate prevention
- **Content Moderation:** Role-based moderation with audit logging
- **Search & Filtering:** Full-text search with tag and status filtering
- **View Tracking:** Automatic view counting (excludes author views)
- **Comprehensive Testing:** 35 passing tests (21 service + 14 controller tests)

**Comments (`/v1/comments`):**

- `POST /v1/comments` - Create new comment on a post (requires authentication and community membership)
- `GET /v1/comments/:id` - Get comment details (respects privacy settings)
- `PUT /v1/comments/:id` - Update comment (author or moderator only)
- `DELETE /v1/comments/:id` - Delete comment (author or moderator only)
- `POST /v1/comments/:id/vote` - Vote on comment (upvote/downvote, prevents self-voting)
- `DELETE /v1/comments/:id/vote` - Remove vote from comment
- `GET /v1/comments/:id/votes` - Get comment vote statistics with user's vote status
- `POST /v1/comments/:id/moderate` - Moderate comment status (moderators and admins only)
- `POST /v1/comments/:id/reply` - Reply to comment (creates threaded replies)
- `GET /v1/posts/:postId/comments` - Get comments for a post with pagination and filtering
- `GET /v1/posts/:postId/comments/tree` - Get threaded comment tree for a post
- `GET /v1/users/:authorId/comments` - Get comments by author (filters private community comments)

**Features:**

- **Threaded Comments:** Full support for nested comment replies with tree structure
- **Permission System:** Community membership validation for commenting and voting
- **Privacy Controls:** Automatic filtering of private community content
- **Vote Management:** Comprehensive voting system with duplicate prevention
- **Content Moderation:** Role-based moderation with audit logging
- **Search & Filtering:** Comment search with status and content filtering
- **Multiple Controllers:** Organized endpoints across 3 controllers (Comments, PostComments, AuthorComments)
- **Comprehensive Testing:** 41 passing tests (23 service + 18 controller tests)

**Tags (`/v1/tags`):**

- `GET /v1/tags` - Browse tags with search, filtering, and pagination (public)
- `GET /v1/tags/stats` - Get tag statistics and analytics (public)
- `GET /v1/tags/suggestions` - Get tag suggestions for autocomplete (public)
- `GET /v1/tags/:name` - Get specific tag details (public)
- `POST /v1/tags` - Create new tag (requires authentication, starts as pending)
- `POST /v1/tags/bulk` - Create multiple tags at once (requires authentication)
- `PUT /v1/tags/:name` - Update tag details (creator only, requires authentication + CSRF)
- `POST /v1/tags/:name/moderate` - Moderate tag status (moderators only, requires authentication + CSRF)
- `POST /v1/tags/moderate/bulk` - Bulk moderate multiple tags (moderators only, requires authentication + CSRF)

**Features:**

- **Pending Approval System:** New tags start as "Pending" and require moderator approval
- **Automatic Deduplication:** Multiple users can reference the same pending tag
- **Tag Normalization:** Automatic conversion to lowercase with spaces replaced by hyphens
- **Character Validation:** Only letters, numbers, and parentheses allowed
- **Tag Names as Primary Keys:** Uses normalized tag names instead of UUIDs
- **Usage Tracking:** Automatic increment when tags are used in posts
- **Search & Filtering:** Full-text search with category and status filtering
- **Bulk Operations:** Moderators can approve/reject multiple tags at once
- **Reserved Names:** Prevents creation of system tags like "admin", "deleted"
- **Comprehensive Testing:** 50 passing tests (22 normalization + 15 service + 13 controller tests)

**System:**

- `GET /` - Basic hello world endpoint
- `GET /v1/health` - Overall system health check
- `POST /v1/init-db` - Initialize database schema (development only)

### 🚧 API Endpoints TODO

**Fishing Spots (`/api/spots`):** ✅ **COMPLETED**

- `GET /api/spots` - List fishing spots with search, filtering, and pagination (supports tags, location, type, status)
- `POST /api/spots` - Create new fishing spot with tag support (requires authentication)
- `GET /api/spots/:id` - Get fishing spot details with tags
- `PUT /api/spots/:id` - Update fishing spot with tag management (creator only)
- `DELETE /api/spots/:id` - Delete fishing spot (creator only)
- `GET /api/spots/nearby` - Find spots near coordinates with geospatial queries
- `POST /api/spots/:id/report` - Report fishing spot for moderation
- `GET /api/spots/creator/:creatorId` - Get fishing spots by creator
- `GET /api/spots/me` - Get current user's fishing spots (requires authentication)

**Features:**

- **Tag Integration:** Full tag support with pending approval workflow (same as posts)
- **Geospatial Queries:** Location-based search with distance filtering
- **Permission System:** Creator-only editing and deletion with proper validation
- **Privacy Controls:** Public/private spot visibility settings
- **Access Information:** Detailed access instructions and requirements
- **Comprehensive DTOs:** Full validation for create/update operations
- **Multiple Controllers:** Organized endpoints across 3 controllers (FishingSpots, CreatorFishingSpots, MyFishingSpots)
- **Comprehensive Testing:** 32 passing tests (18 service + 14 controller tests)

#### **Tags (`/v1/tags`)** ✅ **COMPLETED**

- [x] `GET /v1/tags` - Browse tags with search and filtering
- [x] `POST /v1/tags` - Create new tag (authenticated users, starts as pending)
- [x] `GET /v1/tags/:name/posts` - Get posts with specific tag (via post filtering)
- [x] `POST /v1/tags/:name/moderate` - Moderate tag status (moderators only)
- [x] `POST /v1/tags/moderate/bulk` - Bulk moderate tags (moderators only)

#### **Notifications (`/v1/notifications`)**

- [ ] `GET /v1/notifications` - Get user's notifications
- [ ] `PUT /v1/notifications/:id/read` - Mark notification as read
- [ ] `PUT /v1/notifications/read-all` - Mark all notifications as read
- [ ] `DELETE /v1/notifications/:id` - Delete notification
- [ ] WebSocket gateway for real-time notifications

#### **Content Moderation (`/v1/moderation`)**

- [ ] `GET /v1/moderation/reports` - List reports (moderator only)
- [ ] `PUT /v1/moderation/reports/:id` - Review report (moderator only)
- [ ] `POST /v1/moderation/posts/:id/moderate` - Moderate post
- [ ] `POST /v1/moderation/comments/:id/moderate` - Moderate comment

#### **Analytics (`/v1/analytics`)**

- [ ] `GET /v1/analytics/posts/:id` - Get post analytics (author only)
- [ ] `GET /v1/analytics/communities/:id` - Get community analytics (admin only)
- [ ] `GET /v1/analytics/users/me` - Get user's content analytics

## Implementation Status

### ✅ Completed

- **Project Structure**: Complete NestJS project architecture
- **Database Migration**: Successfully migrated from SurrealDB to PostgreSQL
- **Complete Database Schema**: 13+ tables with proper relationships and indexes
- **TypeORM Integration**: Full entity definitions and repository pattern
- **GitHub OAuth Authentication**: Working end-to-end with httpOnly cookies
- **NextJS Web Frontend**: Complete authentication flow and routing
- **Shared Types System**: Symlinked types for code reuse across platforms
- **Database Migration System**: Ongoing schema management capabilities
- **Comprehensive Type System**: Proper TypeScript enums and interfaces
- **Cross-Origin Authentication**: Cookie-parser middleware for web/mobile
- **Enterprise CSRF Protection**: Automatic token management and validation
- **Security Headers**: Helmet middleware for production-ready security
- **OWASP Top 10 Compliance**: Comprehensive security measures
- **OAuth Token Revocation**: Complete logout with provider token cleanup
- **Users Module**: Complete user management with profiles, search, and social following
- **Communities Module**: Full community management with roles, invitations, and moderation
- **Posts Module**: Complete post management with CRUD operations, voting, and moderation
- **Comments Module**: Complete comment management with threaded replies, voting, and moderation
- **Tags Module**: Complete tag management with pending approval system, normalization, and moderation
- **Fishing Spots Module**: Complete fishing spot management with geospatial queries, tag integration, and privacy controls

### 🚧 In Progress

- Additional OAuth providers (Google, Facebook, Apple)

### 📋 Implementation Priority

**Recommended implementation order for maximum user value:**

1. ~~**Users Module** (Foundation) - Essential for profile management and social features~~ ✅ **COMPLETED**
2. ~~**Communities Module** (Core Social) - Primary community creation and management~~ ✅ **COMPLETED**
3. ~~**Posts Module** (Content Creation) - Main content sharing functionality~~ ✅ **COMPLETED**
4. ~~**Comments Module** (Engagement) - User interaction and discussions~~ ✅ **COMPLETED**
5. ~~**Tags Module** (Organization) - Content categorization and discovery~~ ✅ **COMPLETED**
6. ~~**Fishing Spots Module** (Unique Feature) - Location-based fishing content~~ ✅ **COMPLETED**
7. **Notifications Module** (Engagement) - User activity and real-time updates
8. **Moderation Module** (Community Health) - Content and community management
9. **Analytics Module** (Insights) - Performance metrics and user analytics

### 🎯 Next Session Goals

1. **Notifications Module Implementation**: Begin with user notification system and real-time updates
2. **Create Module Structure**: Set up controller, service, DTOs, and tests for notifications
3. **Implement WebSocket Gateway**: Focus on real-time notification delivery
4. **Add Notification Types**: Implement various notification types (follows, posts, comments, etc.)
5. **Write Tests**: Create comprehensive unit and integration tests for notifications
6. **Update Documentation**: Keep API documentation current

## Security Features

- **CSRF Protection**: All POST/PUT/PATCH/DELETE requests require valid CSRF tokens
- **Rate Limiting**: Configurable throttling on all endpoints (60 requests/minute default)
- **Security Headers**: Comprehensive HTTP security headers via Helmet middleware
- **JWT Authentication**: Secure token-based authentication with httpOnly cookies
- **OAuth Integration**: Secure social authentication with GitHub (more providers planned)
- **Input Validation**: Comprehensive request validation and sanitization
- **Audit Logging**: Security event tracking and monitoring capabilities
- **Session Management**: Secure session handling with device tracking
