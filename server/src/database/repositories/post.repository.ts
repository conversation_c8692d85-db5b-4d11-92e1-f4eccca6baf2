import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  Post,
  User,
  Community,
  FishingSpot,
  Tag,
  PostStatus,
  VoteType,
} from '@/types/global';
import { PostEntity } from '../entities/post.entity';
import { PostVoteEntity } from '../entities/post-vote.entity';
import { UserEntity } from '../entities/user.entity';
import { CommunityEntity } from '../entities/community.entity';
import { FishingSpotEntity } from '../entities/fishing-spot.entity';
import { TagEntity } from '../entities/tag.entity';

@Injectable()
export class PostRepository {
  private readonly logger = new Logger(PostRepository.name);

  constructor(
    @InjectRepository(PostEntity)
    private readonly postRepository: Repository<PostEntity>,
    @InjectRepository(PostVoteEntity)
    private readonly voteRepository: Repository<PostVoteEntity>,
    @InjectRepository(FishingSpotEntity)
    private readonly fishingSpotRepository: Repository<FishingSpotEntity>,
  ) { }

  // Helper method to convert entity to type
  private entityToPost (entity: PostEntity): Post {
    return {
      id: entity.id,
      title: entity.title,
      status: entity.status,
      content: entity.content,
      author: entity.author ? this.entityToUser(entity.author) : null,
      community: entity.community ? this.entityToCommunity(entity.community) : null,
      fishingSpots: entity.fishingSpots ? entity.fishingSpots.map(spot => this.entityToFishingSpot(spot)) : [],
      comments: [], // Will be populated when needed
      tags: entity.tags ? entity.tags.map(tag => this.entityToTag(tag)) : [],
      moderation: undefined, // TODO: Convert entity moderation to full Moderation type when needed
      upvotes: entity.upvotes,
      downvotes: entity.downvotes,
      views: entity.views,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  private entityToUser (entity: UserEntity): User {
    return {
      id: entity.id,
      uname: entity.uname,
      status: entity.status,
      email: entity.email,
      emailVerified: entity.emailVerified,
      profile: entity.profile,
      prefs: entity.prefs,
      activity: entity.activity,
      savedContent: entity.savedContent,
      subscribedCommunities: [],
      providerAccounts: entity.providerAccounts || {},
      lastLoginAt: entity.lastLoginAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  private entityToCommunity (entity: CommunityEntity): Community {
    return {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      rules: entity.rules,
      visibility: entity.visibility,
      invitePermission: entity.invitePermission,
      postModeration: entity.postModeration,
      commentModeration: entity.commentModeration,
      pic: entity.pic,
      banner: entity.banner,
      owner: entity.owner ? this.entityToUser(entity.owner) : null,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  private entityToFishingSpot (entity: FishingSpotEntity): FishingSpot {
    return {
      id: entity.id,
      name: entity.name,
      status: entity.status,
      spotType: entity.spotType,
      isPublic: entity.access?.isPublic || false,
      description: entity.description,
      coordinates: {
        lat: entity.location.latitude,
        lng: entity.location.longitude,
      },
      location: entity.location,
      conditions: entity.conditions,
      access: entity.access,
      typesOfFish: entity.conditions?.fishSpecies || [],
      rating: entity.rating,
      reviewCount: entity.reviewCount,
      createdBy: entity.createdBy ? this.entityToUser(entity.createdBy) : null,
      tags: entity.tags ? entity.tags.map(tag => this.entityToTag(tag)) : [],
      moderation: undefined, // FishingSpot moderation not implemented yet
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  private entityToTag (entity: TagEntity): Tag {
    return {
      name: entity.name,
      displayName: entity.displayName,
      description: entity.description,
      category: entity.category,
      color: entity.color,
      status: entity.status,
      usageCount: entity.usageCount,
      createdBy: entity.creator ? {
        id: entity.creator.id,
        uname: entity.creator.uname,
        status: entity.creator.status,
        email: entity.creator.email,
        emailVerified: entity.creator.emailVerified,
        profile: entity.creator.profile,
        prefs: entity.creator.prefs,
        activity: entity.creator.activity,
        savedContent: entity.creator.savedContent,
        subscribedCommunities: [],
        providerAccounts: entity.creator.providerAccounts,
        lastLoginAt: entity.creator.lastLoginAt,
        createdAt: entity.creator.createdAt,
        updatedAt: entity.creator.updatedAt,
      } : null,
      moderatedBy: entity.moderator ? {
        id: entity.moderator.id,
        uname: entity.moderator.uname,
        status: entity.moderator.status,
        email: entity.moderator.email,
        emailVerified: entity.moderator.emailVerified,
        profile: entity.moderator.profile,
        prefs: entity.moderator.prefs,
        activity: entity.moderator.activity,
        savedContent: entity.moderator.savedContent,
        subscribedCommunities: [],
        providerAccounts: entity.moderator.providerAccounts,
        lastLoginAt: entity.moderator.lastLoginAt,
        createdAt: entity.moderator.createdAt,
        updatedAt: entity.moderator.updatedAt,
      } : null,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      moderatedAt: entity.moderatedAt,
    };
  }

  // Post CRUD operations
  async createPost (postData: Partial<PostEntity>): Promise<Post> {
    try {
      const post = this.postRepository.create(postData);
      const savedPost = await this.postRepository.save(post);

      // Load the post with all relations
      const postWithRelations = await this.postRepository.findOne({
        where: { id: savedPost.id },
        relations: ['author', 'community', 'community.owner', 'fishingSpots', 'fishingSpots.createdBy', 'tags'],
      });

      this.logger.log(`Created post: ${savedPost.title} (${savedPost.id})`);
      return this.entityToPost(postWithRelations);
    } catch (error) {
      this.logger.error(`Failed to create post: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findPostById (id: string): Promise<Post | null> {
    try {
      const post = await this.postRepository.findOne({
        where: { id },
        relations: ['author', 'community', 'community.owner', 'fishingSpots', 'fishingSpots.createdBy', 'tags'],
      });

      return post ? this.entityToPost(post) : null;
    } catch (error) {
      this.logger.error(`Failed to find post by ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updatePost (id: string, updateData: Partial<PostEntity>): Promise<Post> {
    try {
      await this.postRepository.update(id, {
        ...updateData,
        updatedAt: new Date(),
      });

      const updatedPost = await this.postRepository.findOne({
        where: { id },
        relations: ['author', 'community', 'community.owner', 'fishingSpots', 'fishingSpots.createdBy', 'tags'],
      });

      if (!updatedPost) {
        throw new Error('Post not found after update');
      }

      this.logger.log(`Updated post: ${updatedPost.title} (${id})`);
      return this.entityToPost(updatedPost);
    } catch (error) {
      this.logger.error(`Failed to update post ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deletePost (id: string): Promise<void> {
    try {
      await this.postRepository.delete(id);
      this.logger.log(`Deleted post: ${id}`);
    } catch (error) {
      this.logger.error(`Failed to delete post ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Post listing and search
  async findPosts (
    page: number = 1,
    limit: number = 20,
    filters?: {
      communityId?: string;
      authorId?: string;
      status?: PostStatus;
      search?: string;
      tags?: string[];
    }
  ): Promise<{ posts: Post[]; total: number }> {
    try {
      const queryBuilder = this.postRepository
        .createQueryBuilder('post')
        .leftJoinAndSelect('post.author', 'author')
        .leftJoinAndSelect('post.community', 'community')
        .leftJoinAndSelect('community.owner', 'communityOwner')
        .leftJoinAndSelect('post.fishingSpots', 'fishingSpots')
        .leftJoinAndSelect('fishingSpots.createdBy', 'fishingSpotsCreator')
        .leftJoinAndSelect('post.tags', 'tags');

      // Apply filters
      if (filters?.communityId) {
        queryBuilder.andWhere('post.communityId = :communityId', { communityId: filters.communityId });
      }

      if (filters?.authorId) {
        queryBuilder.andWhere('post.authorId = :authorId', { authorId: filters.authorId });
      }

      if (filters?.status) {
        queryBuilder.andWhere('post.status = :status', { status: filters.status });
      } else {
        // Default to published posts only
        queryBuilder.andWhere('post.status = :status', { status: PostStatus.Published });
      }

      if (filters?.search) {
        queryBuilder.andWhere(
          '(post.title ILIKE :search OR post.content ILIKE :search)',
          { search: `%${filters.search}%` }
        );
      }

      if (filters?.tags && filters.tags.length > 0) {
        queryBuilder.andWhere('tags.name IN (:...tagNames)', { tagNames: filters.tags });
      }

      queryBuilder
        .orderBy('post.createdAt', 'DESC')
        .skip((page - 1) * limit)
        .take(limit);

      const [entities, total] = await queryBuilder.getManyAndCount();
      const posts = entities.map(entity => this.entityToPost(entity));

      return { posts, total };
    } catch (error) {
      this.logger.error(`Failed to find posts: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findPostsByCommunity (
    communityId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ posts: Post[]; total: number }> {
    return this.findPosts(page, limit, { communityId, status: PostStatus.Published });
  }

  async findPostsByAuthor (
    authorId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ posts: Post[]; total: number }> {
    return this.findPosts(page, limit, { authorId, status: PostStatus.Published });
  }

  // Post voting operations
  async voteOnPost (userId: string, postId: string, voteType: VoteType): Promise<void> {
    try {
      // Check if user already voted on this post
      const existingVote = await this.voteRepository.findOne({
        where: { userId, postId },
      });

      if (existingVote) {
        if (existingVote.voteType === voteType) {
          // Same vote type, remove the vote
          await this.voteRepository.delete(existingVote.id);
          await this.updateVoteCounts(postId);
          this.logger.log(`Removed ${voteType} vote from post ${postId} by user ${userId}`);
          return;
        } else {
          // Different vote type, update the vote
          await this.voteRepository.update(existingVote.id, { voteType });
          await this.updateVoteCounts(postId);
          this.logger.log(`Changed vote to ${voteType} on post ${postId} by user ${userId}`);
          return;
        }
      }

      // Create new vote
      const vote = this.voteRepository.create({
        userId,
        postId,
        voteType,
      });

      await this.voteRepository.save(vote);
      await this.updateVoteCounts(postId);
      this.logger.log(`Added ${voteType} vote to post ${postId} by user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to vote on post: ${error.message}`, error.stack);
      throw error;
    }
  }

  async removeVote (userId: string, postId: string): Promise<void> {
    try {
      await this.voteRepository.delete({ userId, postId });
      await this.updateVoteCounts(postId);
      this.logger.log(`Removed vote from post ${postId} by user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to remove vote: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getUserVote (userId: string, postId: string): Promise<VoteType | null> {
    try {
      const vote = await this.voteRepository.findOne({
        where: { userId, postId },
      });

      return vote ? vote.voteType : null;
    } catch (error) {
      this.logger.error(`Failed to get user vote: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async updateVoteCounts (postId: string): Promise<void> {
    try {
      const upvotes = await this.voteRepository.count({
        where: { postId, voteType: VoteType.Upvote },
      });

      const downvotes = await this.voteRepository.count({
        where: { postId, voteType: VoteType.Downvote },
      });

      await this.postRepository.update(postId, { upvotes, downvotes });
    } catch (error) {
      this.logger.error(`Failed to update vote counts for post ${postId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Post statistics and analytics
  async incrementViews (postId: string): Promise<void> {
    try {
      await this.postRepository.increment({ id: postId }, 'views', 1);
      this.logger.log(`Incremented views for post ${postId}`);
    } catch (error) {
      this.logger.error(`Failed to increment views for post ${postId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getPostVoteStats (postId: string): Promise<{ upvotes: number; downvotes: number; userVote?: VoteType }> {
    try {
      const post = await this.postRepository.findOne({
        where: { id: postId },
        select: ['upvotes', 'downvotes'],
      });

      if (!post) {
        throw new Error('Post not found');
      }

      return {
        upvotes: post.upvotes,
        downvotes: post.downvotes,
      };
    } catch (error) {
      this.logger.error(`Failed to get post vote stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Fishing spot management methods
  async addFishingSpotsToPost (postId: string, fishingSpotIds: string[]): Promise<Post> {
    try {
      const post = await this.postRepository.findOne({
        where: { id: postId },
        relations: ['fishingSpots'],
      });

      if (!post) {
        throw new Error('Post not found');
      }

      // Find the fishing spots to add
      const fishingSpots = await this.fishingSpotRepository.findByIds(fishingSpotIds);

      // Add the fishing spots to the post
      post.fishingSpots = [...(post.fishingSpots || []), ...fishingSpots];
      await this.postRepository.save(post);

      // Return the updated post with all relations
      const updatedPost = await this.postRepository.findOne({
        where: { id: postId },
        relations: ['author', 'community', 'community.owner', 'fishingSpots', 'fishingSpots.createdBy', 'tags'],
      });

      this.logger.log(`Added ${fishingSpotIds.length} fishing spots to post: ${postId}`);
      return this.entityToPost(updatedPost);
    } catch (error) {
      this.logger.error(`Failed to add fishing spots to post ${postId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async removeFishingSpotsFromPost (postId: string, fishingSpotIds: string[]): Promise<Post> {
    try {
      const post = await this.postRepository.findOne({
        where: { id: postId },
        relations: ['fishingSpots'],
      });

      if (!post) {
        throw new Error('Post not found');
      }

      // Remove the specified fishing spots
      post.fishingSpots = post.fishingSpots.filter(spot => !fishingSpotIds.includes(spot.id));
      await this.postRepository.save(post);

      // Return the updated post with all relations
      const updatedPost = await this.postRepository.findOne({
        where: { id: postId },
        relations: ['author', 'community', 'community.owner', 'fishingSpots', 'fishingSpots.createdBy', 'tags'],
      });

      this.logger.log(`Removed ${fishingSpotIds.length} fishing spots from post: ${postId}`);
      return this.entityToPost(updatedPost);
    } catch (error) {
      this.logger.error(`Failed to remove fishing spots from post ${postId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async replaceFishingSpotsInPost (postId: string, fishingSpotIds: string[]): Promise<Post> {
    try {
      const post = await this.postRepository.findOne({
        where: { id: postId },
        relations: ['fishingSpots'],
      });

      if (!post) {
        throw new Error('Post not found');
      }

      // Find the new fishing spots
      const fishingSpots = fishingSpotIds.length > 0
        ? await this.fishingSpotRepository.findByIds(fishingSpotIds)
        : [];

      // Replace all fishing spots
      post.fishingSpots = fishingSpots;
      await this.postRepository.save(post);

      // Return the updated post with all relations
      const updatedPost = await this.postRepository.findOne({
        where: { id: postId },
        relations: ['author', 'community', 'community.owner', 'fishingSpots', 'fishingSpots.createdBy', 'tags'],
      });

      this.logger.log(`Replaced fishing spots in post: ${postId} with ${fishingSpotIds.length} spots`);
      return this.entityToPost(updatedPost);
    } catch (error) {
      this.logger.error(`Failed to replace fishing spots in post ${postId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Post moderation methods
  async moderatePost (postId: string, moderationData: any): Promise<Post> {
    try {
      await this.postRepository.update(postId, {
        moderation: moderationData,
        updatedAt: new Date(),
      });

      const moderatedPost = await this.postRepository.findOne({
        where: { id: postId },
        relations: ['author', 'community', 'community.owner', 'fishingSpots', 'fishingSpots.createdBy', 'tags'],
      });

      if (!moderatedPost) {
        throw new Error('Post not found after moderation');
      }

      this.logger.log(`Moderated post: ${postId}`);
      return this.entityToPost(moderatedPost);
    } catch (error) {
      this.logger.error(`Failed to moderate post ${postId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updatePostStatus (postId: string, status: PostStatus): Promise<Post> {
    try {
      await this.postRepository.update(postId, {
        status,
        updatedAt: new Date(),
      });

      const updatedPost = await this.postRepository.findOne({
        where: { id: postId },
        relations: ['author', 'community', 'community.owner', 'fishingSpots', 'fishingSpots.createdBy', 'tags'],
      });

      if (!updatedPost) {
        throw new Error('Post not found after status update');
      }

      this.logger.log(`Updated post status to ${status}: ${postId}`);
      return this.entityToPost(updatedPost);
    } catch (error) {
      this.logger.error(`Failed to update post status ${postId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Tag management for posts
  async addTagsToPost (postId: string, tagNames: string[]): Promise<Post> {
    try {
      const post = await this.postRepository.findOne({
        where: { id: postId },
        relations: ['tags'],
      });

      if (!post) {
        throw new Error('Post not found');
      }

      // Add new tags (avoiding duplicates)
      const existingTagNames = post.tags.map(tag => tag.name);
      const newTagNames = tagNames.filter(name => !existingTagNames.includes(name));

      if (newTagNames.length > 0) {
        await this.postRepository
          .createQueryBuilder()
          .relation(PostEntity, 'tags')
          .of(postId)
          .add(newTagNames);
      }

      // Return updated post
      const updatedPost = await this.postRepository.findOne({
        where: { id: postId },
        relations: ['author', 'community', 'community.owner', 'fishingSpots', 'fishingSpots.createdBy', 'tags'],
      });

      this.logger.log(`Added tags to post: ${postId}`);
      return this.entityToPost(updatedPost);
    } catch (error) {
      this.logger.error(`Failed to add tags to post ${postId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async removeTagsFromPost (postId: string, tagNames: string[]): Promise<Post> {
    try {
      await this.postRepository
        .createQueryBuilder()
        .relation(PostEntity, 'tags')
        .of(postId)
        .remove(tagNames);

      // Return updated post
      const updatedPost = await this.postRepository.findOne({
        where: { id: postId },
        relations: ['author', 'community', 'community.owner', 'fishingSpots', 'fishingSpots.createdBy', 'tags'],
      });

      if (!updatedPost) {
        throw new Error('Post not found after removing tags');
      }

      this.logger.log(`Removed tags from post: ${postId}`);
      return this.entityToPost(updatedPost);
    } catch (error) {
      this.logger.error(`Failed to remove tags from post ${postId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
