import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinColumn,
  JoinTable,
  Index,
} from 'typeorm';
import { FishingSpotType, FishingSpotStatus } from '@/types/global';
import { UserEntity } from './user.entity';
import { PostEntity } from './post.entity';
import { TagEntity } from './tag.entity';

@Entity('fishing_spots')
@Index(['name'])
@Index(['status'])
@Index(['location'])
@Index(['createdById'])
export class FishingSpotEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 200 })
  @Index()
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: FishingSpotType,
    name: 'spot_type',
  })
  spotType: FishingSpotType;

  @Column({
    type: 'enum',
    enum: FishingSpotStatus,
    default: FishingSpotStatus.Active,
  })
  @Index()
  status: FishingSpotStatus;

  // Location data as JSONB for flexibility
  @Column({ type: 'jsonb' })
  @Index()
  location: {
    latitude: number;
    longitude: number;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    zipCode?: string;
  };

  // Fishing conditions as JSONB
  @Column({ type: 'jsonb', nullable: true })
  conditions?: {
    waterType: string;
    depth?: string;
    structure?: string[];
    fishSpecies?: string[];
    bestTimes?: string[];
    seasonality?: string;
  };

  // Access information as JSONB
  @Column({ type: 'jsonb', nullable: true })
  access?: {
    isPublic: boolean;
    requiresPermission: boolean;
    fees?: string;
    restrictions?: string[];
    amenities?: string[];
  };

  @Column({ name: 'created_by_id' })
  createdById: string;

  @Column({ default: 0 })
  rating: number;

  @Column({ name: 'review_count', default: 0 })
  reviewCount: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by_id' })
  createdBy: UserEntity;

  @OneToMany(() => PostEntity, (post) => post.fishingSpot)
  posts: PostEntity[];

  // Many-to-many relationship with tags
  @ManyToMany(() => TagEntity, (tag) => tag.fishingSpots)
  @JoinTable({
    name: 'fishing_spot_tags',
    joinColumn: { name: 'fishing_spot_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'tag_name', referencedColumnName: 'name' },
  })
  tags: TagEntity[];
}
